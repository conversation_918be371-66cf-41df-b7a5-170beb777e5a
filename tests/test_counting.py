import os
import asyncio

from dotenv import load_dotenv

from anotherme.run import create_agent
from anotherme.utils.vis import get_adjusted_window_size
from anotherme.utils.logging_setting import set_logging

import sys

sys.path.append(
    os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "anotherme"))
)


load_dotenv(override=True)

recordings_root = "./recordings"
model_id = "google/gemini-2.5-flash-preview-05-20"

recordings_dir = os.path.join(recordings_root, "example")
browser_log_dir = os.path.join(recordings_dir, "browser_logs")
os.makedirs(recordings_dir, exist_ok=True)
log_file = os.path.join(recordings_dir, "output.log")
main_file = os.path.join(recordings_dir, "main.log")
with open(log_file, "w") as f:
    f.write("")
with open(main_file, "w") as f:
    f.write("")
set_logging(log_file)

window_size = get_adjusted_window_size(char_width=7.5, use_terminal_aware=False)

agent = create_agent(
    model_id=model_id,
    browser_log_dir=browser_log_dir,
    window_size=window_size,
    sub_agents=["browser"],
    storage_state="/Users/<USER>/Desktop/code/anotherme/helpers/.auth/fb/fb0.json",
)
agent.logger.register_log_file(main_file, log_file)


async def main():
    await agent.managed_agents["browseruse_agent"].excute_task(
        task="跳转到https://www.facebook.com/ads/library/?active_status=active&ad_type=all&country=ALL&is_targeted_country=false&media_type=all&search_type=page&source=page-transparency-widget&view_all_page_id=248661941892305，调用analyze_and_count_similar_items判断这个页面有多少条广告items以及有多少条items与这个相关：Aarde aan de ruimte! Met de nieuwe ONE van one2track blijf je altijd in contact met jouw kleine astronaut. Uitgerust met stiltetijden, 4 minuten videobellen en een extra lange batterij voor dagenlang avontuur. "
    )

    await asyncio.sleep(3)
    await agent.managed_agents["browseruse_agent"].browser_context.__aexit__(
        None, None, None
    )
    # await agent.managed_agents['browseruse_agent'].browser.close()


asyncio.run(main())
