Dropsure Customer Development: Identifying Independent Store Sellers Based on Meta Ad Performance​​

Fill the corresponding information into the file <excel表格> based on the task list and the given row index. Note: You must fill the table without adding columns. Always remember this.

​​Your task input for row index <行索引>:​​
Blogger link: <博主链接>
Product link: <产品链接>
Ad info: <广告信息>


1. Call the browser use agent with the following code and parameter(don't edit): browseruse_agent("Navigate to the product link: <产品链接>, Retrieve the product's current price, brand name, product category (e.g., handbag, necklace, etc.), brief description, and determine if it is sold by an official flagship store (simply check if the product page contains information about a major platform or brand).Check if the product is listed on 1688. Follow these steps: First, call extract_webpage_images to obtain URLs for 2 product images. Then, use the browser use agent's image_search1688 tool to determine if the product is listed on 1688 (note: evaluate each returned result one by one; if found, stop; if none are found after all checks, confirm it is not listed). Please return the product's current price, brand name, product category, whether it is sold by an official flagship store, and whether it is listed on 1688.") Record the information.

2. Call the browser use agent with the following code and parameter, you must replace the {product brief description} with the information you have extracted earlier : browseruse_agent("Navigate to the blogger link: <博主链接>. Upon loading,  click sequentially on: '简介' , '公共主页信息公示' , extract the blogger's name, follower count, and country (if available) and account creation date on this page by extract_content tool. Then, click '查看全部', and extract the blogger's geographical location (if available). Determine the blogger's country and record it. Finally, click '前往广告资料库' to navigate to the new page. Here, use the analyze_and_count_items tool to count the total ads and ads for distinct products. Return the blogger's name, follower count, account creation date, country, total ads, and the count of ads per product category") Record the information.

3. Compile all collected information, assign a seller rating (1–5) with justification. High scores are given based on: Follower count: Below 10K, the more the better, but is not very important. Account creation date: Preferably older than 6 months (current date: 2025-06-04), the older the better. Admin country: Must not include China. Fewer countries, ideally concentrated in the US or Europe, are preferred. Number of active ads: The more, the better. Product is listed on 1688 is better. Product is not sold by an official flagship store is better. Summarize the reason for failure to get enough information. If any of the following conditions are met, immediately score 0: the blogger’s location includes China, or the product being sold is a service or a high-end luxury item.

4. Map all previously obtained information to the following column names: 博主名称 粉丝数量	博主账号创建日期 管理者国家	商品价格 品牌名 商品类别 是否在1688在售 广告投放总数 不同商品广告投放数 卖家评级 评级理由 失败原因
template:博主名称 | 粉丝数量（纯数字） | 博主账号创建日期（yyyy-mm-dd） | 管理者国家 ｜商品价格(货币符号+float)| 品牌名 ｜ 商品类别 ｜  是否在1688在售（是/否） | 广告投放总数 ｜ 不同商品广告投放数 | 卖家评级 | 评级理由（text, in chinese） ｜ 失败原因（text, in chinese，reason for failure） [case] Lil | 125000 | 2018-05-12 | China | $18.99 | Julia ｜bag｜ 否 | 32 | 10 | 5 | 商品质量好  | sucess

5. Open the <excel表格> file, then write the data into the Excel sheet based on row  <行索引> and the specified column names. You must use this function to write: df.loc[ <行索引>, '博主名称'] = blogger_name. You cannot modify the first parameter  <行索引>, as you can only write this one line.
