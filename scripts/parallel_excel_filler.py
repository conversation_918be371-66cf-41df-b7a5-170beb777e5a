import os
import sys
import time
import pandas as pd
import argparse
import multiprocessing
from multiprocessing import Process, Manager
import subprocess
import logging
import json
from datetime import datetime
import random

# 添加anotherme模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# 导入新的直接执行函数
try:
    from anotherme.demo import run_task_directly
    USE_DIRECT_EXECUTION = True
except ImportError:
    USE_DIRECT_EXECUTION = False
    logging.warning("无法导入run_task_directly函数，将使用原始的subprocess方式")

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("parallel_excel_filler.log"),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger(__name__)


def parse_args():
    parser = argparse.ArgumentParser(description="并行执行Excel填充任务")
    parser.add_argument(
        "--excel_file",
        type=str,
        default="./ListAdContentSchema_4.1_processed.xlsx",
        help="要处理的Excel文件路径",
    )
    parser.add_argument(
        "--task_file",
        type=str,
        default="./data/meta_tables/meta.md",
        help="任务描述文件路径",
    )
    parser.add_argument(
        "--model_id",
        type=str,
        default="openrouter/anthropic/claude-3.7-sonnet",
        help="使用的模型ID",
    )
    parser.add_argument(
        "--concurrent_limit", type=int, default=3, help="并行执行的agent数量"
    )
    parser.add_argument(
        "--start_row",
        type=int,
        default=0,
        help="从Excel的哪一行开始处理（0表示第一行数据）",
    )
    parser.add_argument(
        "--end_row",
        type=int,
        default=None,
        help="处理到Excel的哪一行结束（None表示处理到最后）",
    )
    parser.add_argument(
        "--timeout", type=int, default=1800, help="每个任务的超时时间（秒）"
    )
    parser.add_argument(
        "--headless", action="store_true", help="是否使用无头模式运行浏览器"
    )
    parser.add_argument(
        "--use_terminal_aware", action="store_true", help="是否使用终端感知模式"
    )
    parser.add_argument(
        "--storage_state_dir",
        type=str,
        default="./helpers/.auth/fb",
        help="storage state pool",
    )
    parser.add_argument(
        "--use_direct_execution",
        action="store_true",
        help="使用直接执行模式（更高效，减少CPU和内存使用）",
    )
    return parser.parse_args()


def read_excel_file(file_path, start_row=0, end_row=None):
    """读取Excel文件并返回需要处理的行"""
    logger.info(f"读取Excel文件: {file_path}")
    try:
        df = pd.read_excel(file_path)
        # 检查必要的列是否存在
        required_columns = ["blogger_link", "product_link"]
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logger.error(f"Excel文件缺少必要的列: {', '.join(missing_columns)}")
            return None

        # 截取指定范围的行
        if end_row is None:
            end_row = len(df)
        df_subset = df.iloc[start_row:end_row].copy()

        logger.info(f"成功读取Excel文件，处理行数: {len(df_subset)}")
        return df_subset
    except Exception as e:
        logger.error(f"读取Excel文件时出错: {e}")
        return None


def create_task_for_row(row_data, row_index, task_template):
    row_json = row_data.to_json()

    task_description = {
        "task_type": "excel_fill",
        "row_index": row_index,
        "row_data": json.loads(row_json),
        "task_template": task_template,
    }

    return task_description


def run_agent_process(task_queue, result_queue, args, process_id):
    """在单独的进程中运行agent"""
    logger.info(f"进程 {process_id} 启动")

    while not task_queue.empty():
        try:
            # 尝试从队列获取任务，如果队列为空则退出
            try:
                task = task_queue.get(block=False)
            except Exception:
                break

            row_index = task["row_index"]

            # 获取行数据
            row_data = task["row_data"]
            values = list(row_data.values())
            blogger_link = values[0]
            product_info = values[1]
            product_link = values[2]

            # 创建包含列索引的文件夹
            column_index = row_index + args.start_row
            output_dir = os.path.join("./recordings/parallel", f"column_{column_index}")
            os.makedirs(output_dir, exist_ok=True)

            current_time = datetime.now().strftime("%m-%d-%H-%M")
            temp_task_file = os.path.join(output_dir, f"task_{current_time}.md")

            logger.info(
                f"进程 {process_id} 开始处理行 {row_index}，任务文档为: {temp_task_file}"
            )
            # 读取原始任务模板
            task_template = task["task_template"]

            # 替换任务模板中的占位符
            modified_template = task_template.replace(
                "<行索引>",
                f"{column_index}",
            )
            modified_template = modified_template.replace(
                "<博主链接>", f"{blogger_link}"
            )
            modified_template = modified_template.replace(
                "<产品链接>", f"{product_link}"
            )
            modified_template = modified_template.replace(
                "<广告信息>", f"{product_info}"
            )
            modified_template = modified_template.replace(
                "<excel表格>", f"{args.excel_file}"
            )

            # 写入修改后的任务文件
            with open(temp_task_file, "w", encoding="utf-8") as f:
                f.write(modified_template)
            # 获取storage_state目录下的所有文件
            storage_state_files = [
                f
                for f in os.listdir(args.storage_state_dir)
                if os.path.isfile(os.path.join(args.storage_state_dir, f))
            ]
            if not storage_state_files:
                logger.error(f"storage_state目录为空: {args.storage_state_dir}")
                raise Exception("No storage state files found")

            # 随机选择一个文件
            selected_file = random.choice(storage_state_files)
            storage_state = os.path.join(args.storage_state_dir, selected_file)
            logger.info(f"进程 {process_id} 使用storage state文件: {storage_state}")

            start_time = time.time()

            # 根据参数选择执行方式
            if args.use_direct_execution and USE_DIRECT_EXECUTION:
                # 使用直接执行模式（更高效）
                try:
                    result = run_task_directly(
                        task_file=temp_task_file,
                        model_id=args.model_id,
                        recordings_dir=output_dir,
                        headless=args.headless,
                        storage_state=storage_state,
                        use_terminal_aware=args.use_terminal_aware,
                    )

                    execution_time = time.time() - start_time

                    # 记录结果
                    task_result = {
                        "row_index": row_index,
                        "column_index": column_index,
                        "success": result["success"],
                        "execution_time": execution_time,
                        "task_file": temp_task_file,
                        "cost": result.get("cost"),
                    }

                    if not result["success"]:
                        logger.error(
                            f"进程 {process_id} 处理行 {row_index} (列索引 {column_index}) 失败: {result['error']}"
                        )
                        task_result["error"] = result["error"]
                    else:
                        logger.info(
                            f"进程 {process_id} 处理行 {row_index} (列索引 {column_index}) 成功，耗时: {execution_time:.2f}秒"
                        )

                    result_queue.put(task_result)

                except Exception as e:
                    logger.error(f"进程 {process_id} 直接执行时出错: {e}")
                    result = {
                        "row_index": row_index,
                        "column_index": column_index,
                        "success": False,
                        "error": str(e),
                        "execution_time": time.time() - start_time,
                        "task_file": temp_task_file,
                    }
                    result_queue.put(result)
            else:
                # 使用原始的subprocess方式
                # 构建命令
                cmd = [
                    sys.executable,  # 当前Python解释器路径
                    "anotherme/demo.py",
                    "--task_file",
                    temp_task_file,
                    "--model_id",
                    args.model_id,
                    "--recordings_dir",
                    output_dir,
                    *(["--headless"] if args.headless else []),
                    *(["--use_terminal_aware"] if args.use_terminal_aware else []),
                    "--storage_state",
                    storage_state,
                ]

                try:
                    process = subprocess.Popen(
                        cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True
                    )

                    # 设置超时
                    try:
                        _, stderr = process.communicate(timeout=args.timeout)
                        exit_code = process.returncode

                        # 记录结果
                        result = {
                            "row_index": row_index,
                            "column_index": column_index,
                            "success": exit_code == 0,
                            "exit_code": exit_code,
                            "execution_time": time.time() - start_time,
                            "task_file": temp_task_file,
                        }

                        if exit_code != 0:
                            logger.error(
                                f"进程 {process_id} 处理行 {row_index} (列索引 {column_index}) 失败: {stderr}"
                            )
                            result["error"] = stderr
                        else:
                            logger.info(
                                f"进程 {process_id} 处理行 {row_index} (列索引 {column_index}) 成功，耗时: {result['execution_time']:.2f}秒"
                            )

                        result_queue.put(result)

                    except subprocess.TimeoutExpired:
                        process.kill()
                        logger.error(
                            f"进程 {process_id} 处理行 {row_index} (列索引 {column_index}) 超时"
                        )
                        result = {
                            "row_index": row_index,
                            "column_index": column_index,
                            "success": False,
                            "error": "任务执行超时",
                            "execution_time": args.timeout,
                            "task_file": temp_task_file,
                        }
                        result_queue.put(result)

                except Exception as e:
                    logger.error(f"进程 {process_id} 执行命令时出错: {e}")
                    result = {
                        "row_index": row_index,
                        "column_index": column_index,
                        "success": False,
                        "error": str(e),
                        "execution_time": time.time() - start_time,
                        "task_file": temp_task_file,
                    }
                    result_queue.put(result)

        except Exception as e:
            logger.error(f"进程 {process_id} 处理任务时出错: {e}")

    logger.info(f"进程 {process_id} 完成所有任务")


def main():
    args = parse_args()

    # 显示执行模式信息
    if args.use_direct_execution and USE_DIRECT_EXECUTION:
        logger.info("使用直接执行模式（高效模式）- 减少CPU和内存使用")
    elif args.use_direct_execution and not USE_DIRECT_EXECUTION:
        logger.warning("无法使用直接执行模式，回退到subprocess模式")
    else:
        logger.info("使用subprocess模式（原始模式）")

    df = read_excel_file(args.excel_file, args.start_row, args.end_row)
    if df is None:
        return

    try:
        with open(args.task_file, "r", encoding="utf-8") as f:
            task_template = f.read()
    except Exception as e:
        logger.error(f"读取任务模板文件时出错: {e}")
        return

    manager = Manager()
    task_queue = manager.Queue()
    result_queue = manager.Queue()

    for i, (_, row) in enumerate(df.iterrows()):
        task = create_task_for_row(row, i + args.start_row, task_template)
        task_queue.put(task)

    logger.info(f"创建了 {task_queue.qsize()} 个任务")

    # 创建并启动进程
    processes = []
    for i in range(min(args.concurrent_limit, task_queue.qsize())):
        p = Process(target=run_agent_process, args=(task_queue, result_queue, args, i))
        processes.append(p)
        p.start()

    # 等待所有进程完成
    for p in processes:
        p.join()

    # 收集结果
    results = []
    while not result_queue.empty():
        results.append(result_queue.get())

    # 按行索引排序结果
    results.sort(key=lambda x: x["row_index"])

    summary = {
        "total_rows": len(results),
        "successful_rows": sum(1 for r in results if r["success"]),
        "total_time": sum(r["execution_time"] for r in results),
    }

    logger.info(
        f"所有任务完成。成功: {summary['successful_rows']}/{summary['total_rows']}"
    )
    logger.info(f"总耗时: {summary['total_time']:.2f}秒")


if __name__ == "__main__":
    multiprocessing.freeze_support()  # Windows支持
    main()
