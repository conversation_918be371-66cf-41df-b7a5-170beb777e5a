#!/usr/bin/env python3
"""
示例：如何使用新的高效并行处理功能
"""

import os
import sys
import time
import logging

# 添加anotherme模块路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from anotherme.demo import run_task_directly

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def example_direct_execution():
    """示例：直接执行单个任务"""
    logger.info("示例1: 直接执行单个任务")
    
    # 创建示例任务文件
    task_content = """
# 示例任务

这是一个示例任务，用于测试直接执行功能。

1. 打开浏览器
2. 访问一个网站
3. 截取屏幕截图
4. 保存结果
"""
    
    task_file = "temp_task.md"
    with open(task_file, "w", encoding="utf-8") as f:
        f.write(task_content)
    
    try:
        # 直接执行任务
        result = run_task_directly(
            task_file=task_file,
            model_id="openrouter/anthropic/claude-3.7-sonnet",
            recordings_dir="./recordings/example",
            headless=True,
            storage_state=None,
            use_terminal_aware=False,
        )
        
        logger.info(f"任务执行结果: {result}")
        
        if result["success"]:
            logger.info("任务执行成功!")
            logger.info(f"结果保存在: {result['recordings_dir']}")
            if result["cost"]:
                logger.info(f"执行成本: {result['cost']}")
        else:
            logger.error(f"任务执行失败: {result['error']}")
            
    except Exception as e:
        logger.error(f"执行过程中出错: {e}")
    finally:
        # 清理临时文件
        if os.path.exists(task_file):
            os.remove(task_file)


def example_multiple_tasks():
    """示例：使用线程池执行多个任务"""
    import concurrent.futures
    import threading
    
    logger.info("示例2: 使用线程池执行多个任务")
    
    # 创建多个任务
    tasks = []
    for i in range(3):
        task_content = f"""
# 任务 {i+1}

这是第 {i+1} 个并行任务。

1. 打开浏览器
2. 执行特定操作 {i+1}
3. 保存结果
"""
        task_file = f"temp_task_{i}.md"
        with open(task_file, "w", encoding="utf-8") as f:
            f.write(task_content)
        
        tasks.append({
            "task_file": task_file,
            "recordings_dir": f"./recordings/example_{i}",
            "task_id": i
        })
    
    def execute_task(task_info):
        """执行单个任务的函数"""
        task_id = task_info["task_id"]
        logger.info(f"开始执行任务 {task_id}")
        
        try:
            result = run_task_directly(
                task_file=task_info["task_file"],
                model_id="openrouter/anthropic/claude-3.7-sonnet",
                recordings_dir=task_info["recordings_dir"],
                headless=True,
                storage_state=None,
                use_terminal_aware=False,
            )
            
            result["task_id"] = task_id
            logger.info(f"任务 {task_id} 完成: {'成功' if result['success'] else '失败'}")
            return result
            
        except Exception as e:
            logger.error(f"任务 {task_id} 执行出错: {e}")
            return {
                "task_id": task_id,
                "success": False,
                "error": str(e),
                "recordings_dir": task_info["recordings_dir"]
            }
    
    try:
        # 使用线程池执行任务
        start_time = time.time()
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
            # 提交所有任务
            future_to_task = {
                executor.submit(execute_task, task): task 
                for task in tasks
            }
            
            # 收集结果
            results = []
            for future in concurrent.futures.as_completed(future_to_task):
                try:
                    result = future.result(timeout=300)  # 5分钟超时
                    results.append(result)
                except concurrent.futures.TimeoutError:
                    logger.error("任务执行超时")
                except Exception as e:
                    logger.error(f"任务执行异常: {e}")
        
        end_time = time.time()
        
        # 输出结果统计
        successful_tasks = sum(1 for r in results if r.get("success", False))
        total_time = end_time - start_time
        
        logger.info(f"所有任务完成!")
        logger.info(f"成功: {successful_tasks}/{len(tasks)}")
        logger.info(f"总耗时: {total_time:.2f}秒")
        
        for result in results:
            task_id = result.get("task_id", "unknown")
            status = "成功" if result.get("success", False) else "失败"
            logger.info(f"  任务 {task_id}: {status}")
            if not result.get("success", False):
                logger.info(f"    错误: {result.get('error', 'Unknown error')}")
                
    finally:
        # 清理临时文件
        for task in tasks:
            if os.path.exists(task["task_file"]):
                os.remove(task["task_file"])


def example_resource_monitoring():
    """示例：监控资源使用情况"""
    import psutil
    
    logger.info("示例3: 监控资源使用情况")
    
    def get_resource_usage():
        """获取当前资源使用情况"""
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        return {
            "cpu_percent": cpu_percent,
            "memory_used_gb": memory.used / (1024**3),
            "memory_percent": memory.percent,
        }
    
    # 记录开始时的资源使用
    start_resources = get_resource_usage()
    logger.info(f"开始时资源使用 - CPU: {start_resources['cpu_percent']:.1f}%, "
                f"内存: {start_resources['memory_used_gb']:.2f}GB ({start_resources['memory_percent']:.1f}%)")
    
    # 创建并执行任务
    task_content = """
# 资源监控测试任务

这是一个用于测试资源使用的任务。

1. 打开浏览器
2. 执行一些操作
3. 监控资源使用
"""
    
    task_file = "temp_resource_test.md"
    with open(task_file, "w", encoding="utf-8") as f:
        f.write(task_content)
    
    try:
        start_time = time.time()
        
        result = run_task_directly(
            task_file=task_file,
            model_id="openrouter/anthropic/claude-3.7-sonnet",
            recordings_dir="./recordings/resource_test",
            headless=True,
            storage_state=None,
            use_terminal_aware=False,
        )
        
        end_time = time.time()
        end_resources = get_resource_usage()
        
        execution_time = end_time - start_time
        
        logger.info(f"任务执行完成，耗时: {execution_time:.2f}秒")
        logger.info(f"结束时资源使用 - CPU: {end_resources['cpu_percent']:.1f}%, "
                    f"内存: {end_resources['memory_used_gb']:.2f}GB ({end_resources['memory_percent']:.1f}%)")
        
        # 计算资源使用差异
        cpu_diff = end_resources['cpu_percent'] - start_resources['cpu_percent']
        memory_diff = end_resources['memory_used_gb'] - start_resources['memory_used_gb']
        
        logger.info(f"资源使用变化 - CPU: {cpu_diff:+.1f}%, 内存: {memory_diff:+.3f}GB")
        
        if result["success"]:
            logger.info("任务执行成功!")
        else:
            logger.error(f"任务执行失败: {result['error']}")
            
    except Exception as e:
        logger.error(f"执行过程中出错: {e}")
    finally:
        # 清理临时文件
        if os.path.exists(task_file):
            os.remove(task_file)


def main():
    """主函数：运行所有示例"""
    logger.info("=" * 50)
    logger.info("高效并行处理功能示例")
    logger.info("=" * 50)
    
    try:
        # 示例1: 直接执行单个任务
        example_direct_execution()
        
        logger.info("\n" + "-" * 30)
        
        # 示例2: 使用线程池执行多个任务
        example_multiple_tasks()
        
        logger.info("\n" + "-" * 30)
        
        # 示例3: 监控资源使用情况
        example_resource_monitoring()
        
    except KeyboardInterrupt:
        logger.info("用户中断执行")
    except Exception as e:
        logger.error(f"示例执行出错: {e}")
    
    logger.info("\n" + "=" * 50)
    logger.info("所有示例执行完成")
    logger.info("=" * 50)


if __name__ == "__main__":
    main()
