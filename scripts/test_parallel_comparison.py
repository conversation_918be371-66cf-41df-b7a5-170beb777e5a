#!/usr/bin/env python3
"""
测试脚本：比较原始并行处理和高效并行处理的性能差异
"""

import os
import sys
import time
import psutil
import subprocess
import argparse
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("parallel_comparison.log"),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger(__name__)


def get_system_resources():
    """获取当前系统资源使用情况"""
    cpu_percent = psutil.cpu_percent(interval=1)
    memory = psutil.virtual_memory()
    return {
        "cpu_percent": cpu_percent,
        "memory_used_gb": memory.used / (1024**3),
        "memory_percent": memory.percent,
        "process_count": len(psutil.pids()),
    }


def run_test(script_name, args_list, test_name):
    """运行测试并监控资源使用"""
    logger.info(f"开始测试: {test_name}")
    logger.info(f"执行命令: python {script_name} {' '.join(args_list)}")
    
    # 记录开始时的资源使用
    start_resources = get_system_resources()
    start_time = time.time()
    
    logger.info(f"开始时资源使用 - CPU: {start_resources['cpu_percent']:.1f}%, "
                f"内存: {start_resources['memory_used_gb']:.2f}GB ({start_resources['memory_percent']:.1f}%), "
                f"进程数: {start_resources['process_count']}")
    
    try:
        # 运行脚本
        cmd = [sys.executable, script_name] + args_list
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 监控资源使用
        max_cpu = start_resources['cpu_percent']
        max_memory = start_resources['memory_used_gb']
        max_processes = start_resources['process_count']
        
        # 每5秒检查一次资源使用
        while process.poll() is None:
            time.sleep(5)
            current_resources = get_system_resources()
            max_cpu = max(max_cpu, current_resources['cpu_percent'])
            max_memory = max(max_memory, current_resources['memory_used_gb'])
            max_processes = max(max_processes, current_resources['process_count'])
            
            logger.info(f"当前资源使用 - CPU: {current_resources['cpu_percent']:.1f}%, "
                        f"内存: {current_resources['memory_used_gb']:.2f}GB, "
                        f"进程数: {current_resources['process_count']}")
        
        # 等待进程完成
        stdout, stderr = process.communicate()
        end_time = time.time()
        
        # 记录结束时的资源使用
        end_resources = get_system_resources()
        
        execution_time = end_time - start_time
        
        logger.info(f"测试完成: {test_name}")
        logger.info(f"执行时间: {execution_time:.2f}秒")
        logger.info(f"最大CPU使用: {max_cpu:.1f}%")
        logger.info(f"最大内存使用: {max_memory:.2f}GB")
        logger.info(f"最大进程数: {max_processes}")
        logger.info(f"退出代码: {process.returncode}")
        
        if process.returncode != 0:
            logger.error(f"脚本执行失败:\n{stderr}")
        
        return {
            "test_name": test_name,
            "execution_time": execution_time,
            "max_cpu": max_cpu,
            "max_memory": max_memory,
            "max_processes": max_processes,
            "exit_code": process.returncode,
            "start_resources": start_resources,
            "end_resources": end_resources,
        }
        
    except Exception as e:
        logger.error(f"测试执行出错: {e}")
        return None


def parse_args():
    parser = argparse.ArgumentParser(description="比较并行处理性能")
    parser.add_argument(
        "--excel_file",
        type=str,
        default="./ListAdContentSchema_4.1_processed.xlsx",
        help="要处理的Excel文件路径",
    )
    parser.add_argument(
        "--task_file",
        type=str,
        default="./data/meta_tables/meta.md",
        help="任务描述文件路径",
    )
    parser.add_argument(
        "--concurrent_limit",
        type=int,
        default=2,
        help="并行执行的数量",
    )
    parser.add_argument(
        "--start_row",
        type=int,
        default=0,
        help="从Excel的哪一行开始处理",
    )
    parser.add_argument(
        "--end_row",
        type=int,
        default=5,
        help="处理到Excel的哪一行结束（用于测试）",
    )
    parser.add_argument(
        "--timeout",
        type=int,
        default=600,
        help="每个任务的超时时间（秒）",
    )
    parser.add_argument(
        "--headless",
        action="store_true",
        help="是否使用无头模式运行浏览器",
    )
    parser.add_argument(
        "--storage_state_dir",
        type=str,
        default="./helpers/.auth/fb",
        help="storage state pool",
    )
    return parser.parse_args()


def main():
    args = parse_args()
    
    logger.info("=" * 60)
    logger.info("并行处理性能比较测试")
    logger.info("=" * 60)
    
    # 构建通用参数
    common_args = [
        "--excel_file", args.excel_file,
        "--task_file", args.task_file,
        "--concurrent_limit", str(args.concurrent_limit),
        "--start_row", str(args.start_row),
        "--end_row", str(args.end_row),
        "--timeout", str(args.timeout),
        "--storage_state_dir", args.storage_state_dir,
    ]
    
    if args.headless:
        common_args.append("--headless")
    
    results = []
    
    # 测试1: 高效并行处理（ThreadPoolExecutor）
    logger.info("\n" + "=" * 40)
    logger.info("测试1: 高效并行处理（ThreadPoolExecutor）")
    logger.info("=" * 40)
    
    result1 = run_test(
        "scripts/efficient_parallel_excel_filler.py",
        common_args,
        "高效并行处理"
    )
    if result1:
        results.append(result1)
    
    # 等待系统资源恢复
    logger.info("等待系统资源恢复...")
    time.sleep(30)
    
    # 测试2: 原始并行处理（直接执行模式）
    logger.info("\n" + "=" * 40)
    logger.info("测试2: 原始并行处理（直接执行模式）")
    logger.info("=" * 40)
    
    result2 = run_test(
        "scripts/parallel_excel_filler.py",
        common_args + ["--use_direct_execution"],
        "原始并行处理（直接执行）"
    )
    if result2:
        results.append(result2)
    
    # 等待系统资源恢复
    logger.info("等待系统资源恢复...")
    time.sleep(30)
    
    # 测试3: 原始并行处理（subprocess模式）
    logger.info("\n" + "=" * 40)
    logger.info("测试3: 原始并行处理（subprocess模式）")
    logger.info("=" * 40)
    
    result3 = run_test(
        "scripts/parallel_excel_filler.py",
        common_args,
        "原始并行处理（subprocess）"
    )
    if result3:
        results.append(result3)
    
    # 输出比较结果
    logger.info("\n" + "=" * 60)
    logger.info("性能比较结果")
    logger.info("=" * 60)
    
    if results:
        for result in results:
            logger.info(f"\n{result['test_name']}:")
            logger.info(f"  执行时间: {result['execution_time']:.2f}秒")
            logger.info(f"  最大CPU使用: {result['max_cpu']:.1f}%")
            logger.info(f"  最大内存使用: {result['max_memory']:.2f}GB")
            logger.info(f"  最大进程数: {result['max_processes']}")
            logger.info(f"  退出代码: {result['exit_code']}")
        
        # 找出最佳性能
        if len(results) > 1:
            best_time = min(results, key=lambda x: x['execution_time'])
            best_cpu = min(results, key=lambda x: x['max_cpu'])
            best_memory = min(results, key=lambda x: x['max_memory'])
            
            logger.info(f"\n性能最佳:")
            logger.info(f"  最快执行: {best_time['test_name']} ({best_time['execution_time']:.2f}秒)")
            logger.info(f"  最低CPU: {best_cpu['test_name']} ({best_cpu['max_cpu']:.1f}%)")
            logger.info(f"  最低内存: {best_memory['test_name']} ({best_memory['max_memory']:.2f}GB)")
    
    logger.info("\n测试完成!")


if __name__ == "__main__":
    main()
