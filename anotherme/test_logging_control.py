#!/usr/bin/env python3
"""
测试日志控制功能
"""

import subprocess
import sys
import os
import tempfile

def test_logging_control():
    """测试日志控制功能"""
    print("🧪 测试日志控制功能")
    print("=" * 50)
    
    # 创建临时任务文件
    task_content = """
# 测试任务

这是一个简单的测试任务，用于验证日志控制功能。

## 任务目标
- 测试日志输出控制
- 验证disable_all_logs参数
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as f:
        f.write(task_content)
        temp_task_file = f.name
    
    try:
        # 测试1: 正常模式（有日志输出）
        print("\n📝 测试1: 正常模式")
        cmd1 = [
            sys.executable,
            "efficient_parallel_excel_filler.py",
            "--help"
        ]
        
        result1 = subprocess.run(cmd1, capture_output=True, text=True, timeout=10)
        
        if "--disable_all_logs" in result1.stdout:
            print("✅ 参数 --disable_all_logs 存在")
        else:
            print("❌ 参数 --disable_all_logs 不存在")
            print("输出:", result1.stdout)
            return False
        
        # 测试2: 测试set_logging函数
        print("\n📝 测试2: 测试set_logging函数")
        test_code = """
import sys
sys.path.append('.')
from anotherme.utils.logging_setting import set_logging
import logging
import tempfile
import os

# 创建临时日志文件
with tempfile.NamedTemporaryFile(mode='w', suffix='.log', delete=False) as f:
    log_file = f.name

try:
    # 测试禁用控制台输出
    set_logging(log_file, disable_console=True)
    
    # 获取日志器并测试
    logger = logging.getLogger("anotherme.browser_use.agent.service")
    logger.info("这条消息应该只出现在文件中，不在控制台")
    
    print("✅ set_logging函数测试通过")
    
    # 检查日志文件是否有内容
    with open(log_file, 'r') as f:
        content = f.read()
        if "这条消息应该只出现在文件中" in content:
            print("✅ 日志文件写入正常")
        else:
            print("❌ 日志文件写入失败")
            
finally:
    # 清理临时文件
    if os.path.exists(log_file):
        os.remove(log_file)
"""
        
        result2 = subprocess.run([sys.executable, "-c", test_code], 
                                capture_output=True, text=True, timeout=10)
        
        if result2.returncode == 0:
            print("✅ set_logging函数测试通过")
            print("输出:", result2.stdout.strip())
        else:
            print("❌ set_logging函数测试失败")
            print("错误:", result2.stderr)
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists(temp_task_file):
            os.remove(temp_task_file)

def main():
    """主函数"""
    print("🚀 日志控制功能测试")
    print("=" * 50)
    
    if test_logging_control():
        print("\n🎉 所有测试通过!")
        print("\n💡 现在可以使用 --disable_all_logs 参数来禁用控制台日志输出:")
        print("python efficient_parallel_excel_filler.py \\")
        print("    --excel_file ../your_file.xlsx \\")
        print("    --task_file ../your_task.md \\")
        print("    --disable_all_logs \\")
        print("    --headless")
    else:
        print("\n❌ 测试失败，请检查错误信息")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
