import logging

formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")


def set_logging(
    main_log_path="output.log",
    logger_name="anotherme.browser_use.agent.service",
    disable_console=False
):
    """
    设置日志配置

    Args:
        main_log_path: 日志文件路径
        logger_name: 日志器名称
        disable_console: 是否禁用控制台输出
    """
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    logger = logging.getLogger(logger_name)
    logger.setLevel(logging.DEBUG)

    if not logger.handlers:
        # 添加文件处理器
        file_handler = logging.FileHandler(main_log_path, mode="a", encoding="utf-8")
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    # 如果需要禁用控制台输出
    if disable_console:
        # 移除所有StreamHandler（控制台输出）
        import sys
        handlers_to_remove = []
        for handler in logger.handlers:
            if isinstance(handler, logging.StreamHandler) and handler.stream in [sys.stdout, sys.stderr]:
                handlers_to_remove.append(handler)

        for handler in handlers_to_remove:
            logger.removeHandler(handler)

        # 确保不传播到父日志器
        logger.propagate = False

        # 同时禁用根日志器的控制台输出
        root_logger = logging.getLogger()
        root_handlers_to_remove = []
        for handler in root_logger.handlers:
            if isinstance(handler, logging.StreamHandler) and handler.stream in [sys.stdout, sys.stderr]:
                root_handlers_to_remove.append(handler)

        for handler in root_handlers_to_remove:
            root_logger.removeHandler(handler)
