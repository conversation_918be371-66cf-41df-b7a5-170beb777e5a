import os
import sys
import time
import pandas as pd
import argparse
import multiprocessing
from multiprocessing import Process, Manager
import logging
import json
from datetime import datetime
import random

from demo import run_task_directly

# 设置主进程日志（只记录到文件，不输出到控制台）
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("efficient_parallel_excel_filler.log"),
        # 主进程不输出到控制台，使用print进行简洁输出
    ],
)
logger = logging.getLogger(__name__)


def parse_args():
    parser = argparse.ArgumentParser(description="高效并行执行Excel填充任务")
    parser.add_argument(
        "--excel_file",
        type=str,
        default="./ListAdContentSchema_4.1_processed.xlsx",
        help="要处理的Excel文件路径",
    )
    parser.add_argument(
        "--task_file",
        type=str,
        default="./data/meta_tables/meta.md",
        help="任务描述文件路径",
    )
    parser.add_argument(
        "--model_id",
        type=str,
        default="openrouter/anthropic/claude-3.7-sonnet",
        help="使用的模型ID",
    )
    parser.add_argument(
        "--concurrent_limit", type=int, default=3, help="并行执行的进程数量"
    )
    parser.add_argument(
        "--start_row",
        type=int,
        default=0,
        help="从Excel的哪一行开始处理（0表示第一行数据）",
    )
    parser.add_argument(
        "--end_row",
        type=int,
        default=None,
        help="处理到Excel的哪一行结束（None表示处理到最后）",
    )
    parser.add_argument(
        "--timeout", type=int, default=1800, help="每个任务的超时时间（秒）"
    )
    parser.add_argument(
        "--headless", action="store_true", help="是否使用无头模式运行浏览器"
    )
    parser.add_argument(
        "--use_terminal_aware", action="store_true", help="是否使用终端感知模式"
    )
    parser.add_argument(
        "--storage_state_dir",
        type=str,
        default="./helpers/.auth/fb",
        help="storage state pool",
    )
    parser.add_argument(
        "--disable_browser_logs",
        action="store_true",
        help="禁用browser_use组件的控制台日志输出（只保留文件日志）",
    )
    return parser.parse_args()


def read_excel_file(file_path, start_row=0, end_row=None):
    """读取Excel文件并返回需要处理的行"""
    print(f"📖 读取Excel文件: {file_path}")
    logger.info(f"读取Excel文件: {file_path}")
    try:
        df = pd.read_excel(file_path)
        # 检查必要的列是否存在
        required_columns = ["blogger_link", "product_link"]
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"❌ Excel文件缺少必要的列: {', '.join(missing_columns)}")
            logger.error(f"Excel文件缺少必要的列: {', '.join(missing_columns)}")
            return None

        # 截取指定范围的行
        if end_row is None:
            end_row = len(df)
        df_subset = df.iloc[start_row:end_row].copy()

        print(f"✅ 成功读取Excel文件，处理行数: {len(df_subset)}")
        logger.info(f"成功读取Excel文件，处理行数: {len(df_subset)}")
        return df_subset
    except Exception as e:
        print(f"❌ 读取Excel文件时出错: {e}")
        logger.error(f"读取Excel文件时出错: {e}")
        return None


def create_task_for_row(row_data, row_index, task_template):
    row_json = row_data.to_json()

    task_description = {
        "task_type": "excel_fill",
        "row_index": row_index,
        "row_data": json.loads(row_json),
        "task_template": task_template,
    }

    return task_description


def run_worker_process(task_queue, result_queue, args, process_id):
    """在单独的进程中运行任务的工作函数"""
    # 在子进程中重新导入模块
    from demo import run_task_directly

    # 为子进程设置独立的日志文件，不输出到控制台
    process_log_file = f"process_{process_id}_parallel_excel_filler.log"
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler(process_log_file),
            # 移除StreamHandler，避免子进程输出到控制台
        ],
        force=True  # 强制重新配置日志
    )
    logger = logging.getLogger(__name__)

    # 只在主进程的日志中记录进程启动信息
    print(f"🚀 进程 {process_id} 启动，日志文件: {process_log_file}")
    
    while not task_queue.empty():
        try:
            # 尝试从队列获取任务，如果队列为空则退出
            try:
                task = task_queue.get(block=False)
            except Exception:
                break

            row_index = task["row_index"]
            
            # 获取行数据
            row_data = task["row_data"]
            values = list(row_data.values())
            blogger_link = values[0]
            product_info = values[1]
            product_link = values[2]

            # 创建包含列索引的文件夹
            column_index = row_index + args.start_row
            output_dir = os.path.join("./recordings/parallel", f"column_{column_index}")
            os.makedirs(output_dir, exist_ok=True)

            current_time = datetime.now().strftime("%m-%d-%H-%M")
            temp_task_file = os.path.join(output_dir, f"task_{current_time}.md")

            # 简洁的控制台输出
            print(f"📝 进程 {process_id} 处理行 {row_index} -> {temp_task_file}")

            # 详细信息记录到日志文件
            logger.info(f"开始处理行 {row_index}，任务文档: {temp_task_file}")
            
            # 读取原始任务模板
            task_template = task["task_template"]

            # 替换任务模板中的占位符
            modified_template = task_template.replace(
                "<行索引>",
                f"{column_index}",
            )
            modified_template = modified_template.replace(
                "<博主链接>", f"{blogger_link}"
            )
            modified_template = modified_template.replace(
                "<产品链接>", f"{product_link}"
            )
            modified_template = modified_template.replace(
                "<广告信息>", f"{product_info}"
            )
            modified_template = modified_template.replace(
                "<excel表格>", f"{args.excel_file}"
            )

            # 写入修改后的任务文件
            with open(temp_task_file, "w", encoding="utf-8") as f:
                f.write(modified_template)
                
            # 获取storage_state目录下的所有文件
            storage_state_files = [
                f
                for f in os.listdir(args.storage_state_dir)
                if os.path.isfile(os.path.join(args.storage_state_dir, f))
            ]
            if not storage_state_files:
                logger.error(f"storage_state目录为空: {args.storage_state_dir}")
                raise Exception("No storage state files found")

            # 随机选择一个文件
            selected_file = random.choice(storage_state_files)
            storage_state = os.path.join(args.storage_state_dir, selected_file)
            # 详细信息记录到日志文件
            logger.info(f"使用storage state文件: {storage_state}")

            start_time = time.time()
            
            # 直接调用run_task_directly函数，而不是创建新进程
            result = run_task_directly(
                task_file=temp_task_file,
                model_id=args.model_id,
                recordings_dir=output_dir,
                headless=args.headless,
                storage_state=storage_state,
                use_terminal_aware=args.use_terminal_aware,
                disable_browser_logs=args.disable_browser_logs,
            )
            
            execution_time = time.time() - start_time
            
            # 记录结果
            task_result = {
                "row_index": row_index,
                "column_index": column_index,
                "success": result["success"],
                "execution_time": execution_time,
                "task_file": temp_task_file,
                "cost": result.get("cost"),
                "process_id": process_id,
            }

            if not result["success"]:
                # 简洁的控制台输出
                print(f"❌ 进程 {process_id} 行 {row_index} 失败: {result.get('error', 'Unknown error')[:50]}...")
                # 详细错误记录到日志文件
                logger.error(f"处理行 {row_index} (列索引 {column_index}) 失败: {result['error']}")
                task_result["error"] = result["error"]
            else:
                # 简洁的控制台输出
                print(f"✅ 进程 {process_id} 行 {row_index} 成功 ({execution_time:.1f}s)")
                # 详细信息记录到日志文件
                logger.info(f"处理行 {row_index} (列索引 {column_index}) 成功，耗时: {execution_time:.2f}秒")

            result_queue.put(task_result)

        except Exception as e:
            # 简洁的控制台输出
            print(f"💥 进程 {process_id} 异常: {str(e)[:50]}...")
            # 详细错误记录到日志文件
            logger.error(f"处理任务时出错: {e}")
            result = {
                "row_index": row_index if 'row_index' in locals() else -1,
                "column_index": column_index if 'column_index' in locals() else -1,
                "success": False,
                "error": str(e),
                "execution_time": time.time() - start_time if 'start_time' in locals() else 0,
                "task_file": temp_task_file if 'temp_task_file' in locals() else None,
                "process_id": process_id,
            }
            result_queue.put(result)

    # 简洁的控制台输出
    print(f"🏁 进程 {process_id} 完成")
    # 详细信息记录到日志文件
    logger.info(f"进程 {process_id} 完成所有任务")


def main():
    args = parse_args()

    print("🚀 高效并行Excel填充任务启动")
    print(f"📁 Excel文件: {args.excel_file}")
    print(f"📄 任务文件: {args.task_file}")
    print(f"🔧 并发进程数: {args.concurrent_limit}")
    print(f"📊 处理范围: 行 {args.start_row} - {args.end_row or '末尾'}")
    print(f"🔇 浏览器日志: {'禁用控制台输出' if args.disable_browser_logs else '正常输出'}")
    print("-" * 50)

    df = read_excel_file(args.excel_file, args.start_row, args.end_row)
    if df is None:
        print("❌ Excel文件读取失败")
        return

    try:
        with open(args.task_file, "r", encoding="utf-8") as f:
            task_template = f.read()
        print(f"✅ 任务模板加载成功")
    except Exception as e:
        print(f"❌ 读取任务模板文件失败: {e}")
        logger.error(f"读取任务模板文件时出错: {e}")
        return

    # 创建任务列表
    tasks = []
    for i, (_, row) in enumerate(df.iterrows()):
        task = create_task_for_row(row, i + args.start_row, task_template)
        tasks.append(task)

    print(f"📋 创建了 {len(tasks)} 个任务")
    logger.info(f"创建了 {len(tasks)} 个任务")

    # 使用multiprocessing进行并行处理
    manager = Manager()
    task_queue = manager.Queue()
    result_queue = manager.Queue()

    # 将任务放入队列
    for task in tasks:
        task_queue.put(task)

    concurrent_processes = min(args.concurrent_limit, len(tasks))
    print(f"🔄 启动 {concurrent_processes} 个并行进程")
    logger.info(f"任务队列大小: {task_queue.qsize()}")

    # 创建并启动进程
    processes = []
    for i in range(concurrent_processes):
        p = Process(target=run_worker_process, args=(task_queue, result_queue, args, i))
        processes.append(p)
        p.start()

    print("⏳ 等待所有进程完成...")
    # 等待所有进程完成
    for p in processes:
        p.join()

    print("📊 收集处理结果...")
    # 收集结果
    results = []
    while not result_queue.empty():
        results.append(result_queue.get())

    # 按行索引排序结果
    results.sort(key=lambda x: x["row_index"])

    summary = {
        "total_rows": len(results),
        "successful_rows": sum(1 for r in results if r["success"]),
        "total_time": sum(r["execution_time"] for r in results),
    }

    # 简洁的控制台总结
    print(f"\n🎉 所有任务完成!")
    print(f"✅ 成功: {summary['successful_rows']}/{summary['total_rows']}")
    print(f"⏱️  总耗时: {summary['total_time']:.1f}秒")

    # 显示失败的任务
    failed_results = [r for r in results if not r["success"]]
    if failed_results:
        print(f"❌ 失败任务:")
        for result in failed_results[:5]:  # 只显示前5个失败任务
            print(f"   行 {result['row_index']}: {result.get('error', 'Unknown error')[:60]}...")
        if len(failed_results) > 5:
            print(f"   ... 还有 {len(failed_results) - 5} 个失败任务")

    print(f"📝 详细日志请查看: efficient_parallel_excel_filler.log 和 process_*_parallel_excel_filler.log")

    # 详细结果记录到日志文件
    logger.info(f"所有任务完成。成功: {summary['successful_rows']}/{summary['total_rows']}")
    logger.info(f"总耗时: {summary['total_time']:.2f}秒")

    for result in results:
        status = "成功" if result["success"] else "失败"
        logger.info(f"行 {result['row_index']} (列索引 {result['column_index']}): {status}, 耗时: {result['execution_time']:.2f}秒")
        if not result["success"]:
            logger.info(f"  错误: {result.get('error', 'Unknown error')}")


if __name__ == "__main__":
    multiprocessing.freeze_support()  # Windows和macOS支持
    main()
