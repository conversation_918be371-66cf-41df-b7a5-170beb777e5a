import argparse
from dotenv import load_dotenv

import os
import re
import rich
import rich.box
from rich.text import Text
from rich.prompt import Prompt
from rich.console import Console
from rich.panel import Panel
from rich.align import Align

from anotherme.run import create_agent
from anotherme.utils.vis import logo, get_adjusted_window_size
from anotherme.utils.logging_setting import set_logging

QUANTUM_PURPLE = "#7e4fff"
LogLevel_INFO = 20

load_dotenv(override=True)


def _disable_browser_console_logs():
    """禁用browser_use组件的控制台日志输出"""
    import logging
    import os
    import sys

    # 设置环境变量来控制browser_use日志级别
    os.environ["BROWSER_USE_LOGGING_LEVEL"] = "error"

    # 获取根日志器
    root_logger = logging.getLogger()

    # 移除所有StreamHandler（控制台输出）
    handlers_to_remove = []
    for handler in root_logger.handlers:
        if isinstance(handler, logging.StreamHandler) and handler.stream in [sys.stdout, sys.stderr]:
            handlers_to_remove.append(handler)

    for handler in handlers_to_remove:
        root_logger.removeHandler(handler)

    # 禁用browser_use相关的日志器的控制台输出
    browser_loggers = [
        "browser_use",
        "anotherme.browser_use",
        "anotherme.browser_use.agent",
        "anotherme.browser_use.agent.service",
        "anotherme.browser_use.browser",
        "anotherme.browser_use.dom",
        "anotherme.browser_use.controller",
    ]

    for logger_name in browser_loggers:
        logger = logging.getLogger(logger_name)
        # 移除StreamHandler
        handlers_to_remove = []
        for handler in logger.handlers:
            if isinstance(handler, logging.StreamHandler) and handler.stream in [sys.stdout, sys.stderr]:
                handlers_to_remove.append(handler)

        for handler in handlers_to_remove:
            logger.removeHandler(handler)

        # 设置为ERROR级别，减少输出
        logger.setLevel(logging.ERROR)
        # 不传播到父日志器
        logger.propagate = False


def parse_md_to_plan(md_text):
    lines = md_text.strip().split("\n")
    task = []
    steps = []

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # Check if line starts with number for step
        if re.match(r"^\d+\.", line):
            step = line.split(".", 1)[1].strip()
            steps.append(step)
        else:
            task.append(line)

    return {"task": "\n".join(task), "steps": steps}


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--model_id", type=str, default="o3-mini")
    parser.add_argument("--task_file", type=str, default=None)
    parser.add_argument("--recordings_root", type=str, default="./recordings")
    parser.add_argument("--recordings_dir", type=str, default=None)
    parser.add_argument("--headless", action="store_true")
    parser.add_argument("--storage_state", type=str, default=None)
    parser.add_argument(
        "--window_position",
        type=lambda s: eval(f"dict({s})"),
        default={"width": 0, "height": 0},
        help="Window position as key-value pairs, e.g. 'width=100,height=200'",
    )
    parser.add_argument("--use_terminal_aware", action="store_true")
    return parser.parse_args()


def run_task_directly(
    task_file,
    model_id="openrouter/anthropic/claude-3.7-sonnet",
    recordings_dir=None,
    recordings_root="./recordings",
    headless=False,
    storage_state=None,
    window_position={"width": 0, "height": 0},
    use_terminal_aware=False,
    disable_browser_logs=False,
):
    """
    直接运行任务，不创建新的Python进程
    用于并行处理时提高效率
    """
    try:
        # 控制browser_use日志输出
        if disable_browser_logs:
            _disable_browser_console_logs()

        # char_width is the terminal font width, which needs to be adjusted based on the actual font being used
        # Default value of 7.5 is used for most monospace fonts, but may need adjustment for different fonts
        window_size = get_adjusted_window_size(
            char_width=7.5, use_terminal_aware=use_terminal_aware
        )

        with open(task_file, "r", encoding="utf-8") as file:
            md_text = file.read()
        parsed_content = parse_md_to_plan(md_text)

        filename = os.path.basename(task_file)

        if recordings_dir is not None:
            recordings_dir = recordings_dir
        else:
            task_name_without_ext = os.path.splitext(filename)[0]
            recordings_dir = os.path.join(recordings_root, task_name_without_ext)

        browser_log_dir = os.path.join(recordings_dir, "browser_logs")
        os.makedirs(recordings_dir, exist_ok=True)
        log_file = os.path.join(recordings_dir, "output.log")
        main_file = os.path.join(recordings_dir, "main.log")
        with open(log_file, "w") as f:
            f.write("")
        with open(main_file, "w") as f:
            f.write("")
        set_logging(log_file)

        agent = create_agent(
            model_id=model_id,
            browser_log_dir=browser_log_dir,
            window_size=window_size,
            window_position=window_position,
            sub_agents=["browser"],
            headless=headless,
            storage_state=storage_state,
        )
        agent.logger.register_log_file(main_file, log_file)

        parsed_content["task"] += f"\nPlease create file in {recordings_dir}"
        agent._get_plan_from_user(parsed_content)
        cost = agent._run_to_completion_cost()

        return {
            "success": True,
            "cost": cost,
            "recordings_dir": recordings_dir,
            "error": None
        }
    except Exception as e:
        return {
            "success": False,
            "cost": None,
            "recordings_dir": recordings_dir if 'recordings_dir' in locals() else None,
            "error": str(e)
        }


def main():
    args = parse_args()
    # char_width is the terminal font width, which needs to be adjusted based on the actual font being used
    # Default value of 7.5 is used for most monospace fonts, but may need adjustment for different fonts
    window_size = get_adjusted_window_size(
        char_width=7.5, use_terminal_aware=args.use_terminal_aware
    )

    logo()

    if args.task_file is None:
        # TODO: logs
        agent = create_agent(
            model_id=args.model_id,
            window_size=window_size,
        )
        console = Console()
        content = Text()
        content.append("Welcome to Simplex AI!\n", style="bold white")
        content.append(
            "We are your dedicated AI Agent assistant, designed to solve complex challenges, analyze data, and provide intelligent insights across domains."
        )

        panel = Panel(
            Align(content, align="left"),
            border_style="white",
            padding=(1, 4),
            box=rich.box.ROUNDED,
        )
        input_prompt = Text("Please enter your question: ", style="bold white")
        console.print(panel)
        user_input = Prompt.ask(
            input_prompt,
            console=console,
            password=False,
            default=None,
            show_default=False,
            choices=None,
        )
        agent.run(user_input)
    else:
        with open(args.task_file, "r", encoding="utf-8") as file:
            md_text = file.read()
        parsed_content = parse_md_to_plan(md_text)

        recordings_root = args.recordings_root
        filename = os.path.basename(args.task_file)

        if args.recordings_dir is not None:
            recordings_dir = args.recordings_dir
        else:
            task_name_without_ext = os.path.splitext(filename)[0]
            recordings_dir = os.path.join(recordings_root, task_name_without_ext)

        browser_log_dir = os.path.join(recordings_dir, "browser_logs")
        os.makedirs(recordings_dir, exist_ok=True)
        log_file = os.path.join(recordings_dir, "output.log")
        main_file = os.path.join(recordings_dir, "main.log")
        with open(log_file, "w") as f:
            f.write("")
        with open(main_file, "w") as f:
            f.write("")
        set_logging(log_file)

        agent = create_agent(
            model_id=args.model_id,
            browser_log_dir=browser_log_dir,
            window_size=window_size,
            window_position=args.window_position,
            sub_agents=["browser"],
            headless=args.headless,
            storage_state=args.storage_state,
        )
        agent.logger.register_log_file(main_file, log_file)

        parsed_content["task"] += f"\nPlease create file in {recordings_dir}"
        agent._get_plan_from_user(parsed_content)
        cost = agent._run_to_completion_cost()
        print(cost)


if __name__ == "__main__":
    main()
