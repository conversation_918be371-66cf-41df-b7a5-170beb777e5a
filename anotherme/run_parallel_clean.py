#!/usr/bin/env python3
"""
简洁的并行处理启动脚本
自动使用最佳配置，输出最简洁的信息
"""

import subprocess
import sys
import argparse

def main():
    parser = argparse.ArgumentParser(description="简洁的并行Excel填充任务")
    parser.add_argument("--excel_file", type=str, required=True, help="Excel文件路径")
    parser.add_argument("--task_file", type=str, required=True, help="任务文件路径")
    parser.add_argument("--concurrent_limit", type=int, default=3, help="并发进程数")
    parser.add_argument("--start_row", type=int, default=0, help="开始行")
    parser.add_argument("--end_row", type=int, default=None, help="结束行")
    parser.add_argument("--storage_state_dir", type=str, default="./helpers/.auth/fb", help="存储状态目录")
    
    args = parser.parse_args()
    
    print("🚀 启动简洁并行处理模式")
    print(f"📁 Excel: {args.excel_file}")
    print(f"📄 任务: {args.task_file}")
    print(f"🔧 并发: {args.concurrent_limit}")
    print(f"🔇 日志: 完全静默模式")
    print("-" * 50)
    
    # 构建命令
    cmd = [
        sys.executable,
        "efficient_parallel_excel_filler.py",
        "--excel_file", args.excel_file,
        "--task_file", args.task_file,
        "--concurrent_limit", str(args.concurrent_limit),
        "--storage_state_dir", args.storage_state_dir,
        "--disable_all_logs",  # 禁用所有日志
        "--headless",  # 无头模式
    ]
    
    if args.start_row is not None:
        cmd.extend(["--start_row", str(args.start_row)])
    
    if args.end_row is not None:
        cmd.extend(["--end_row", str(args.end_row)])
    
    try:
        # 执行命令
        result = subprocess.run(cmd, check=True)
        print("\n✅ 任务完成!")
        return result.returncode
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 任务失败，退出码: {e.returncode}")
        return e.returncode
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
