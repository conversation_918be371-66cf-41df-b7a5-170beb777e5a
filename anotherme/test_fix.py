#!/usr/bin/env python3
"""
测试修复后的并行处理脚本
"""

import subprocess
import sys
import os

def test_basic_functionality():
    """测试基本功能是否正常"""
    print("🧪 测试修复后的并行处理脚本")
    print("=" * 50)
    
    # 检查必要文件
    required_files = [
        "efficient_parallel_excel_filler.py",
        "demo.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    
    print("✅ 所有必要文件检查通过")
    
    # 测试脚本语法
    try:
        result = subprocess.run([
            sys.executable, "-m", "py_compile", "efficient_parallel_excel_filler.py"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 脚本语法检查通过")
        else:
            print("❌ 脚本语法错误:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ 语法检查失败: {e}")
        return False
    
    # 测试帮助信息
    try:
        result = subprocess.run([
            sys.executable, "efficient_parallel_excel_filler.py", "--help"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and "--disable_all_logs" in result.stdout:
            print("✅ 帮助信息正常，包含新参数")
        else:
            print("❌ 帮助信息异常")
            print(result.stdout)
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ 帮助信息测试失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🚀 修复验证测试")
    print("=" * 50)
    
    if test_basic_functionality():
        print("\n🎉 所有基本测试通过!")
        print("\n💡 现在可以使用以下命令测试完整功能:")
        print("python efficient_parallel_excel_filler.py \\")
        print("    --excel_file ../your_file.xlsx \\")
        print("    --task_file ../your_task.md \\")
        print("    --concurrent_limit 2 \\")
        print("    --disable_all_logs \\")
        print("    --headless")
    else:
        print("\n❌ 测试失败，请检查错误信息")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
