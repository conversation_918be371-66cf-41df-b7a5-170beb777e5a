#!/usr/bin/env python3
"""
测试高效并行处理脚本的输出效果
"""

import subprocess
import sys
import os

def test_parallel_output():
    """测试并行处理的输出效果"""
    print("🧪 测试高效并行处理脚本的输出效果")
    print("=" * 50)
    
    # 构建测试命令
    cmd = [
        sys.executable,
        "efficient_parallel_excel_filler.py",
        "--excel_file", "../ListAdContentSchema_4.1_processed.xlsx",
        "--task_file", "../data/meta_tables/meta.md",
        "--concurrent_limit", "2",
        "--start_row", "0",
        "--end_row", "3",  # 只处理前3行进行测试
        "--headless",
        "--disable_all_logs",  # 禁用所有日志输出
        "--timeout", "300"  # 5分钟超时
    ]
    
    print("执行命令:")
    print(" ".join(cmd))
    print("-" * 50)
    
    try:
        # 实时显示输出
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 实时读取并显示输出
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())
        
        # 等待进程完成
        return_code = process.poll()
        
        print("-" * 50)
        print(f"进程退出码: {return_code}")
        
        if return_code == 0:
            print("✅ 测试完成!")
        else:
            print("❌ 测试失败!")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
        process.terminate()
    except Exception as e:
        print(f"❌ 测试执行出错: {e}")

def show_log_files():
    """显示生成的日志文件"""
    print("\n📝 生成的日志文件:")
    
    log_files = [
        "efficient_parallel_excel_filler.log",
        "process_0_parallel_excel_filler.log",
        "process_1_parallel_excel_filler.log"
    ]
    
    for log_file in log_files:
        if os.path.exists(log_file):
            size = os.path.getsize(log_file)
            print(f"  ✅ {log_file} ({size} bytes)")
        else:
            print(f"  ❌ {log_file} (不存在)")

def show_output_directories():
    """显示生成的输出目录"""
    print("\n📁 生成的输出目录:")
    
    recordings_dir = "./recordings/parallel"
    if os.path.exists(recordings_dir):
        subdirs = [d for d in os.listdir(recordings_dir) 
                  if os.path.isdir(os.path.join(recordings_dir, d))]
        if subdirs:
            print(f"  📂 {recordings_dir}/")
            for subdir in sorted(subdirs):
                subdir_path = os.path.join(recordings_dir, subdir)
                files = os.listdir(subdir_path)
                print(f"    📁 {subdir}/ ({len(files)} 文件)")
        else:
            print(f"  📂 {recordings_dir}/ (空)")
    else:
        print(f"  ❌ {recordings_dir} (不存在)")

def main():
    """主函数"""
    print("🚀 高效并行处理测试工具")
    print("=" * 50)
    
    # 检查必要文件
    required_files = [
        "efficient_parallel_excel_filler.py",
        "../ListAdContentSchema_4.1_processed.xlsx",
        "../data/meta_tables/meta.md"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        print("\n请确保所有必要文件存在后再运行测试。")
        return
    
    print("✅ 所有必要文件检查通过")
    print()
    
    # 运行测试
    test_parallel_output()
    
    # 显示结果
    show_log_files()
    show_output_directories()
    
    print("\n" + "=" * 50)
    print("测试完成!")
    print("\n💡 提示:")
    print("- 简洁的进度信息显示在控制台")
    print("- 详细的日志信息保存在日志文件中")
    print("- 每个进程有独立的日志文件")
    print("- 任务输出保存在 recordings/parallel/ 目录下")

if __name__ == "__main__":
    main()
